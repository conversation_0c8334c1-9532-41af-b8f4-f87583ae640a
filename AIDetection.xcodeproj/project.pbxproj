// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001 /* AIDetectionApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000000 /* AIDetectionApp.swift */; };
		A1000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002 /* ContentView.swift */; };
		A1000005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000004 /* Assets.xcassets */; };
		A1000008 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000007 /* Preview Assets.xcassets */; };
		A1000010 /* CameraView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100000F /* CameraView.swift */; };
		A1000012 /* ModelListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000011 /* ModelListView.swift */; };
		A1000014 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000013 /* SettingsView.swift */; };
		A1000016 /* CameraViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000015 /* CameraViewModel.swift */; };
		A1000018 /* ModelManagerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000017 /* ModelManagerViewModel.swift */; };
		A100001A /* SettingsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000019 /* SettingsViewModel.swift */; };
		A100001C /* CameraService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001B /* CameraService.swift */; };
		A100001E /* DetectionService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001D /* DetectionService.swift */; };
		A1000020 /* ModelManagerService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001F /* ModelManagerService.swift */; };
		A1000022 /* DetectionResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000021 /* DetectionResult.swift */; };
		A1000024 /* MLModelInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000023 /* MLModelInfo.swift */; };
		A1000026 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000025 /* Extensions.swift */; };
		A1000028 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000027 /* Constants.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A0FFFFFF /* AIDetection.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AIDetection.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000000 /* AIDetectionApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIDetectionApp.swift; sourceTree = "<group>"; };
		A1000002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1000004 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1000007 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A100000F /* CameraView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraView.swift; sourceTree = "<group>"; };
		A1000011 /* ModelListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModelListView.swift; sourceTree = "<group>"; };
		A1000013 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A1000015 /* CameraViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraViewModel.swift; sourceTree = "<group>"; };
		A1000017 /* ModelManagerViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModelManagerViewModel.swift; sourceTree = "<group>"; };
		A1000019 /* SettingsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsViewModel.swift; sourceTree = "<group>"; };
		A100001B /* CameraService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CameraService.swift; sourceTree = "<group>"; };
		A100001D /* DetectionService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DetectionService.swift; sourceTree = "<group>"; };
		A100001F /* ModelManagerService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModelManagerService.swift; sourceTree = "<group>"; };
		A1000021 /* DetectionResult.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DetectionResult.swift; sourceTree = "<group>"; };
		A1000023 /* MLModelInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MLModelInfo.swift; sourceTree = "<group>"; };
		A1000025 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		A1000027 /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		A1000029 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A0FFFFFC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A0FFFFFB /* AIDetection */ = {
			isa = PBXGroup;
			children = (
				A1000029 /* Info.plist */,
				A100002A /* App */,
				A100002B /* Views */,
				A100002C /* ViewModels */,
				A100002D /* Models */,
				A100002E /* Services */,
				A100002F /* Utilities */,
				A1000004 /* Assets.xcassets */,
				A1000006 /* Preview Content */,
			);
			path = AIDetection;
			sourceTree = "<group>";
		};
		A1000006 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1000007 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A100002A /* App */ = {
			isa = PBXGroup;
			children = (
				A1000000 /* AIDetectionApp.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		A100002B /* Views */ = {
			isa = PBXGroup;
			children = (
				A1000002 /* ContentView.swift */,
				A100000F /* CameraView.swift */,
				A1000011 /* ModelListView.swift */,
				A1000013 /* SettingsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A100002C /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				A1000015 /* CameraViewModel.swift */,
				A1000017 /* ModelManagerViewModel.swift */,
				A1000019 /* SettingsViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A100002D /* Models */ = {
			isa = PBXGroup;
			children = (
				A1000021 /* DetectionResult.swift */,
				A1000023 /* MLModelInfo.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A100002E /* Services */ = {
			isa = PBXGroup;
			children = (
				A100001B /* CameraService.swift */,
				A100001D /* DetectionService.swift */,
				A100001F /* ModelManagerService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A100002F /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A1000025 /* Extensions.swift */,
				A1000027 /* Constants.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		A0FFFFFA = {
			isa = PBXGroup;
			children = (
				A0FFFFFB /* AIDetection */,
				A1000000 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000000 /* Products */ = {
			isa = PBXGroup;
			children = (
				A0FFFFFF /* AIDetection.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A0FFFFFE /* AIDetection */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A100000B /* Build configuration list for PBXNativeTarget "AIDetection" */;
			buildPhases = (
				A0FFFFFB /* Sources */,
				A0FFFFFC /* Frameworks */,
				A0FFFFFD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AIDetection;
			productName = AIDetection;
			productReference = A0FFFFFF /* AIDetection.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A0FFFFF7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A0FFFFFE = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A0FFFFFA /* Build configuration list for PBXProject "AIDetection" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A0FFFFFA;
			productRefGroup = A1000000 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A0FFFFFE /* AIDetection */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A0FFFFFD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000008 /* Preview Assets.xcassets in Resources */,
				A1000005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A0FFFFFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000003 /* ContentView.swift in Sources */,
				A1000001 /* AIDetectionApp.swift in Sources */,
				A1000010 /* CameraView.swift in Sources */,
				A1000012 /* ModelListView.swift in Sources */,
				A1000014 /* SettingsView.swift in Sources */,
				A1000016 /* CameraViewModel.swift in Sources */,
				A1000018 /* ModelManagerViewModel.swift in Sources */,
				A100001A /* SettingsViewModel.swift in Sources */,
				A100001C /* CameraService.swift in Sources */,
				A100001E /* DetectionService.swift in Sources */,
				A1000020 /* ModelManagerService.swift in Sources */,
				A1000022 /* DetectionResult.swift in Sources */,
				A1000024 /* MLModelInfo.swift in Sources */,
				A1000026 /* Extensions.swift in Sources */,
				A1000028 /* Constants.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		******** /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A100000A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A100000C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AIDetection/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AIDetection/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to perform real-time object detection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.aidetection.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A100000D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AIDetection/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AIDetection/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to perform real-time object detection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Screen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.aidetection.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A0FFFFFA /* Build configuration list for PBXProject "AIDetection" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				******** /* Debug */,
				A100000A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A100000B /* Build configuration list for PBXNativeTarget "AIDetection" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A100000C /* Debug */,
				A100000D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A0FFFFF7 /* Project object */;
}
