//
//  CameraView.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import AVFoundation

struct CameraView: View {
    @EnvironmentObject var viewModel: CameraViewModel
    @State private var showingPermissionAlert = false
    @State private var showingErrorAlert = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Camera Preview
                CameraPreviewView(session: viewModel.getCaptureSession())
                    .ignoresSafeArea()
                
                // Detection Overlays
                if viewModel.isDetectionEnabled && !viewModel.currentDetections.isEmpty {
                    DetectionOverlayView(
                        detections: viewModel.currentDetections,
                        frameSize: viewModel.frameSize,
                        showConfidenceScores: viewModel.showConfidenceScores
                    )
                }
                
                // Top Controls
                VStack {
                    HStack {
                        // Current Model Indicator
                        if let model = viewModel.currentModel {
                            ModelIndicatorView(model: model)
                        }
                        
                        Spacer()
                        
                        // Processing Indicator
                        if viewModel.isProcessing {
                            ProcessingIndicatorView()
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)
                    
                    Spacer()
                    
                    // Bottom Controls
                    BottomControlsView()
                        .environmentObject(viewModel)
                }
            }
            .navigationTitle("AI Detection")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: viewModel.clearDetections) {
                        Image(systemName: "clear")
                            .foregroundColor(.white)
                    }
                }
            }
        }
        .onAppear {
            handleCameraPermission()
        }
        .alert("Camera Permission Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                openAppSettings()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text(Constants.ErrorMessages.cameraPermissionDenied)
        }
        .alert("Error", isPresented: $showingErrorAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(viewModel.error ?? "An unknown error occurred")
        }
        .onChange(of: viewModel.cameraPermissionStatus) { status in
            handlePermissionChange(status)
        }
        .onChange(of: viewModel.error) { error in
            showingErrorAlert = error != nil
        }
    }
    
    private func handleCameraPermission() {
        switch viewModel.cameraPermissionStatus {
        case .authorized:
            viewModel.startCamera()
        case .notDetermined:
            viewModel.requestCameraPermission()
        case .denied, .restricted:
            showingPermissionAlert = true
        @unknown default:
            break
        }
    }
    
    private func handlePermissionChange(_ status: AVAuthorizationStatus) {
        switch status {
        case .authorized:
            viewModel.startCamera()
            showingPermissionAlert = false
        case .denied, .restricted:
            showingPermissionAlert = true
        default:
            break
        }
    }
    
    private func openAppSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

// MARK: - Camera Preview View
struct CameraPreviewView: UIViewRepresentable {
    let session: AVCaptureSession
    
    func makeUIView(context: Context) -> CameraPreviewUIView {
        let view = CameraPreviewUIView()
        view.session = session
        return view
    }
    
    func updateUIView(_ uiView: CameraPreviewUIView, context: Context) {
        // Update if needed
    }
}

class CameraPreviewUIView: UIView {
    var session: AVCaptureSession? {
        didSet {
            guard let session = session else { return }
            previewLayer.session = session
        }
    }
    
    override class var layerClass: AnyClass {
        return AVCaptureVideoPreviewLayer.self
    }
    
    private var previewLayer: AVCaptureVideoPreviewLayer {
        return layer as! AVCaptureVideoPreviewLayer
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        previewLayer.frame = bounds
        previewLayer.videoGravity = .resizeAspectFill
    }
}

// MARK: - Detection Overlay View
struct DetectionOverlayView: View {
    let detections: [DetectionResult]
    let frameSize: CGSize
    let showConfidenceScores: Bool
    
    var body: some View {
        GeometryReader { geometry in
            ForEach(detections) { detection in
                DetectionBoxView(
                    detection: detection,
                    containerSize: geometry.size,
                    frameSize: frameSize,
                    showConfidence: showConfidenceScores
                )
            }
        }
    }
}

// MARK: - Detection Box View
struct DetectionBoxView: View {
    let detection: DetectionResult
    let containerSize: CGSize
    let frameSize: CGSize
    let showConfidence: Bool
    
    private var scaledBoundingBox: CGRect {
        let scaleX = containerSize.width / frameSize.width
        let scaleY = containerSize.height / frameSize.height
        let scale = min(scaleX, scaleY)
        
        let scaledWidth = frameSize.width * scale
        let scaledHeight = frameSize.height * scale
        
        let offsetX = (containerSize.width - scaledWidth) / 2
        let offsetY = (containerSize.height - scaledHeight) / 2
        
        return CGRect(
            x: offsetX + detection.boundingBox.origin.x * scaledWidth,
            y: offsetY + detection.boundingBox.origin.y * scaledHeight,
            width: detection.boundingBox.width * scaledWidth,
            height: detection.boundingBox.height * scaledHeight
        )
    }
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            Rectangle()
                .stroke(Color.confidenceColor(for: detection.confidence), lineWidth: Constants.detectionBoxLineWidth)
                .frame(width: scaledBoundingBox.width, height: scaledBoundingBox.height)
            
            Text(showConfidence ? detection.displayText : detection.label)
                .font(.system(size: Constants.detectionLabelFontSize, weight: .semibold))
                .foregroundColor(.white)
                .padding(Constants.detectionLabelPadding)
                .background(Color.confidenceColor(for: detection.confidence))
                .cornerRadius(4)
                .offset(x: 0, y: -30)
        }
        .position(
            x: scaledBoundingBox.midX,
            y: scaledBoundingBox.midY
        )
    }
}

// MARK: - Supporting Views
struct ModelIndicatorView: View {
    let model: MLModelInfo
    
    var body: some View {
        HStack {
            Image(systemName: model.modelType.icon)
            Text(model.name)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.backgroundOverlay)
        .foregroundColor(.white)
        .cornerRadius(20)
    }
}

struct ProcessingIndicatorView: View {
    var body: some View {
        HStack {
            ProgressView()
                .scaleEffect(0.8)
            Text("Processing")
                .font(.caption)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.backgroundOverlay)
        .foregroundColor(.white)
        .cornerRadius(20)
    }
}

struct BottomControlsView: View {
    @EnvironmentObject var viewModel: CameraViewModel
    
    var body: some View {
        VStack(spacing: 16) {
            // Detection Toggle
            HStack {
                Text("Detection")
                    .foregroundColor(.white)
                    .fontWeight(.medium)
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { viewModel.isDetectionEnabled },
                    set: { _ in viewModel.toggleDetection() }
                ))
                .toggleStyle(SwitchToggleStyle(tint: .green))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color.backgroundOverlay)
            .cornerRadius(Constants.cornerRadius)
            
            // Confidence Threshold Slider
            if viewModel.isDetectionEnabled {
                VStack {
                    HStack {
                        Text("Confidence")
                            .foregroundColor(.white)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("\(Int(viewModel.confidenceThreshold * 100))%")
                            .foregroundColor(.white)
                            .fontWeight(.medium)
                    }
                    
                    Slider(
                        value: Binding(
                            get: { viewModel.confidenceThreshold },
                            set: { viewModel.updateConfidenceThreshold($0) }
                        ),
                        in: 0.1...1.0,
                        step: 0.05
                    )
                    .accentColor(.white)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color.backgroundOverlay)
                .cornerRadius(Constants.cornerRadius)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, 30)
    }
}

#Preview {
    CameraView()
        .environmentObject(CameraViewModel())
}
