//
//  ModelListView.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

struct ModelListView: View {
    @EnvironmentObject var viewModel: ModelManagerViewModel
    @EnvironmentObject var cameraViewModel: CameraViewModel
    @State private var showingErrorAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // Bundled Models Section
                if !viewModel.bundledModels.isEmpty {
                    Section("Bundled Models") {
                        ForEach(viewModel.bundledModels) { model in
                            ModelRowView(
                                model: model,
                                isSelected: model.id == viewModel.selectedModel?.id,
                                downloadProgress: viewModel.getDownloadProgress(for: model),
                                isDownloading: viewModel.isDownloading(model)
                            ) {
                                selectModel(model)
                            } onDelete: {
                                // Cannot delete bundled models
                            } onCancelDownload: {
                                viewModel.cancelDownload(for: model.id)
                            }
                        }
                    }
                }
                
                // Downloaded Models Section
                if !viewModel.downloadedModels.isEmpty {
                    Section("Downloaded Models") {
                        ForEach(viewModel.downloadedModels) { model in
                            ModelRowView(
                                model: model,
                                isSelected: model.id == viewModel.selectedModel?.id,
                                downloadProgress: viewModel.getDownloadProgress(for: model),
                                isDownloading: viewModel.isDownloading(model)
                            ) {
                                selectModel(model)
                            } onDelete: {
                                viewModel.deleteModel(model)
                            } onCancelDownload: {
                                viewModel.cancelDownload(for: model.id)
                            }
                        }
                    }
                }
                
                // Empty State
                if viewModel.availableModels.isEmpty && !viewModel.isLoading {
                    Section {
                        VStack(spacing: 16) {
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 48))
                                .foregroundColor(.secondary)
                            
                            Text("No Models Available")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            Text("Download a CoreML model to get started with object detection.")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            
                            Button("Add Sample Model") {
                                viewModel.addSampleModel()
                                viewModel.showAddModelSheet()
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding(.vertical, 32)
                        .frame(maxWidth: .infinity)
                    }
                }
            }
            .navigationTitle("Models")
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    Button(action: viewModel.refreshModels) {
                        Image(systemName: "arrow.clockwise")
                    }
                    
                    Button(action: viewModel.showAddModelSheet) {
                        Image(systemName: "plus")
                    }
                }
            }
            .refreshable {
                viewModel.refreshModels()
            }
            .sheet(isPresented: $viewModel.showingAddModelSheet) {
                AddModelSheet()
                    .environmentObject(viewModel)
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(viewModel.error ?? "An unknown error occurred")
            }
            .onChange(of: viewModel.error) { error in
                showingErrorAlert = error != nil
            }
        }
    }
    
    private func selectModel(_ model: MLModelInfo) {
        viewModel.selectModel(model)
        cameraViewModel.loadModel(model)
    }
}

// MARK: - Model Row View
struct ModelRowView: View {
    let model: MLModelInfo
    let isSelected: Bool
    let downloadProgress: Double
    let isDownloading: Bool
    let onSelect: () -> Void
    let onDelete: () -> Void
    let onCancelDownload: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: model.modelType.icon)
                        .foregroundColor(.blue)
                    
                    Text(model.name)
                        .font(.headline)
                        .lineLimit(1)
                    
                    if model.isBundled {
                        Text("BUNDLED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.2))
                            .foregroundColor(.blue)
                            .cornerRadius(4)
                    }
                    
                    Spacer()
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }
                }
                
                if !model.description.isEmpty {
                    Text(model.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                HStack {
                    Text(model.modelType.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(4)
                    
                    Text(model.formattedFileSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let imageSize = model.inputImageSize {
                        Text(imageSize.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                
                // Download Progress
                if isDownloading {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Downloading...")
                                .font(.caption)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text("\(Int(downloadProgress * 100))%")
                                .font(.caption)
                                .foregroundColor(.blue)
                            
                            Button("Cancel") {
                                onCancelDownload()
                            }
                            .font(.caption)
                            .foregroundColor(.red)
                        }
                        
                        ProgressView(value: downloadProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    }
                }
            }
            
            Spacer()
        }
        .contentShape(Rectangle())
        .onTapGesture {
            if model.isReadyToUse && !isDownloading {
                onSelect()
            }
        }
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            if !model.isBundled {
                Button("Delete", role: .destructive) {
                    onDelete()
                }
            }
        }
        .opacity(model.isReadyToUse ? 1.0 : 0.6)
    }
}

// MARK: - Add Model Sheet
struct AddModelSheet: View {
    @EnvironmentObject var viewModel: ModelManagerViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Form {
                Section("Model Information") {
                    TextField("Model Name", text: $viewModel.newModelName)
                    TextField("Description (Optional)", text: $viewModel.newModelDescription)
                }
                
                Section("Download URL") {
                    TextField("https://example.com/model.mlmodel", text: $viewModel.newModelURL)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                }
                
                Section("Quick Add") {
                    Button("Add YOLOv3 Sample") {
                        viewModel.addSampleModel()
                    }
                    .foregroundColor(.blue)
                }
                
                Section {
                    Text("Note: Only .mlmodel files are supported. Large models may take time to download.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Add Model")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Download") {
                        viewModel.downloadModel()
                        dismiss()
                    }
                    .disabled(viewModel.newModelName.isEmpty || viewModel.newModelURL.isEmpty)
                }
            }
        }
    }
}

#Preview {
    ModelListView()
        .environmentObject(ModelManagerViewModel())
        .environmentObject(CameraViewModel())
}
