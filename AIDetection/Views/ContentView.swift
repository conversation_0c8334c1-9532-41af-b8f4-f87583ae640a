//
//  ContentView.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var cameraViewModel = CameraViewModel()
    @StateObject private var modelManagerViewModel = ModelManagerViewModel()
    @StateObject private var settingsViewModel = SettingsViewModel()
    
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Camera Tab
            CameraView()
                .environmentObject(cameraViewModel)
                .tabItem {
                    Image(systemName: "camera.viewfinder")
                    Text("Camera")
                }
                .tag(0)
            
            // Models Tab
            ModelListView()
                .environmentObject(modelManagerViewModel)
                .environmentObject(cameraViewModel)
                .tabItem {
                    Image(systemName: "brain.head.profile")
                    Text("Models")
                }
                .tag(1)
            
            // Settings Tab
            SettingsView()
                .environmentObject(settingsViewModel)
                .environmentObject(cameraViewModel)
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(2)
        }
        .accentColor(.blue)
        .onAppear {
            setupAppearance()
        }
    }
    
    private func setupAppearance() {
        // Configure tab bar appearance
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
    }
}
