//
//  Constants.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation

struct Constants {
    
    // MARK: - App Configuration
    static let appName = "AI Detection"
    static let appVersion = "1.0"
    
    // MARK: - Detection Settings
    static let defaultConfidenceThreshold: Float = 0.5
    static let frameProcessingInterval = 5 // Process every 5th frame
    static let maxDetectionsPerFrame = 10
    
    // MARK: - Model Management
    static let modelsDirectoryName = "MLModels"
    static let bundledModelsDirectoryName = "BundledModels"
    static let modelFileExtension = "mlmodel"
    static let compiledModelExtension = "mlmodelc"
    
    // MARK: - UI Constants
    static let cornerRadius: CGFloat = 12
    static let standardPadding: CGFloat = 16
    static let smallPadding: CGFloat = 8
    static let largeSpacing: CGFloat = 24
    
    // MARK: - Detection Box Styling
    static let detectionBoxLineWidth: CGFloat = 2.0
    static let detectionLabelFontSize: CGFloat = 14
    static let detectionLabelPadding: CGFloat = 4
    
    // MARK: - Animation Durations
    static let standardAnimationDuration: Double = 0.3
    static let quickAnimationDuration: Double = 0.15
    
    // MARK: - Network Configuration
    static let downloadTimeoutInterval: TimeInterval = 30.0
    static let maxConcurrentDownloads = 3
    
    // MARK: - File Size Limits
    static let maxModelFileSizeMB = 500 // 500MB limit for model files
    
    // MARK: - UserDefaults Keys
    struct UserDefaultsKeys {
        static let selectedModelID = "selectedModelID"
        static let confidenceThreshold = "confidenceThreshold"
        static let isDetectionEnabled = "isDetectionEnabled"
        static let frameProcessingInterval = "frameProcessingInterval"
        static let showConfidenceScores = "showConfidenceScores"
    }
    
    // MARK: - Notification Names
    struct NotificationNames {
        static let modelDownloadProgress = "ModelDownloadProgress"
        static let modelDownloadCompleted = "ModelDownloadCompleted"
        static let modelDownloadFailed = "ModelDownloadFailed"
        static let detectionStateChanged = "DetectionStateChanged"
    }
    
    // MARK: - Error Messages
    struct ErrorMessages {
        static let cameraPermissionDenied = "Camera permission is required for object detection. Please enable it in Settings."
        static let cameraNotAvailable = "Camera is not available on this device."
        static let modelLoadingFailed = "Failed to load the selected model. Please try a different model."
        static let modelDownloadFailed = "Failed to download the model. Please check your internet connection."
        static let invalidModelFormat = "The selected model format is not supported."
        static let insufficientStorage = "Not enough storage space to download the model."
    }
    
    // MARK: - Sample Model URLs (for demonstration)
    struct SampleModels {
        static let yoloV3URL = "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3/YOLOv3.mlmodel"
        static let mobileNetURL = "https://ml-assets.apple.com/coreml/models/Image/Classification/MobileNetV2/MobileNetV2.mlmodel"
    }
}
