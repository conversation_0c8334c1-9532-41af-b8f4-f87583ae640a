//
//  Extensions.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import AVFoundation
import CoreGraphics

// MARK: - Color Extensions
extension Color {
    /// Creates a color from hex string
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// App-specific colors
    static let detectionBoxColor = Color.green
    static let highConfidenceColor = Color.green
    static let mediumConfidenceColor = Color.orange
    static let lowConfidenceColor = Color.red
    static let backgroundOverlay = Color.black.opacity(0.3)
    
    /// Returns color based on confidence level
    static func confidenceColor(for confidence: Float) -> Color {
        switch confidence {
        case 0.7...1.0:
            return .highConfidenceColor
        case 0.4..<0.7:
            return .mediumConfidenceColor
        default:
            return .lowConfidenceColor
        }
    }
}

// MARK: - View Extensions
extension View {
    /// Applies a card-like appearance
    func cardStyle() -> some View {
        self
            .background(Color(.systemBackground))
            .cornerRadius(Constants.cornerRadius)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    /// Applies standard padding
    func standardPadding() -> some View {
        self.padding(Constants.standardPadding)
    }
    
    /// Conditional view modifier
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// MARK: - CGRect Extensions
extension CGRect {
    /// Scales the rectangle by the given factors
    func scaled(by scale: CGSize) -> CGRect {
        return CGRect(
            x: origin.x * scale.width,
            y: origin.y * scale.height,
            width: size.width * scale.width,
            height: size.height * scale.height
        )
    }
    
    /// Returns the center point of the rectangle
    var center: CGPoint {
        return CGPoint(x: midX, y: midY)
    }
    
    /// Converts from Vision coordinate system to UIKit coordinate system
    func convertFromVisionToUIKit(in containerSize: CGSize) -> CGRect {
        let x = origin.x * containerSize.width
        let y = (1 - origin.y - height) * containerSize.height
        let width = size.width * containerSize.width
        let height = size.height * containerSize.height
        
        return CGRect(x: x, y: y, width: width, height: height)
    }
}

// MARK: - AVCaptureVideoOrientation Extensions
extension AVCaptureVideoOrientation {
    /// Creates orientation from UIDeviceOrientation
    init?(deviceOrientation: UIDeviceOrientation) {
        switch deviceOrientation {
        case .portrait:
            self = .portrait
        case .portraitUpsideDown:
            self = .portraitUpsideDown
        case .landscapeLeft:
            self = .landscapeRight
        case .landscapeRight:
            self = .landscapeLeft
        default:
            return nil
        }
    }
    
    /// Creates orientation from UIInterfaceOrientation
    init?(interfaceOrientation: UIInterfaceOrientation) {
        switch interfaceOrientation {
        case .portrait:
            self = .portrait
        case .portraitUpsideDown:
            self = .portraitUpsideDown
        case .landscapeLeft:
            self = .landscapeLeft
        case .landscapeRight:
            self = .landscapeRight
        default:
            return nil
        }
    }
}

// MARK: - String Extensions
extension String {
    /// Capitalizes the first letter of the string
    var capitalizedFirst: String {
        return prefix(1).capitalized + dropFirst()
    }
    
    /// Removes file extension from string
    var withoutExtension: String {
        return (self as NSString).deletingPathExtension
    }
    
    /// Returns file extension
    var fileExtension: String {
        return (self as NSString).pathExtension
    }
}

// MARK: - URL Extensions
extension URL {
    /// Returns the file size in bytes
    var fileSize: Int64 {
        do {
            let resourceValues = try resourceValues(forKeys: [.fileSizeKey])
            return Int64(resourceValues.fileSize ?? 0)
        } catch {
            return 0
        }
    }
    
    /// Returns whether the file exists
    var fileExists: Bool {
        return FileManager.default.fileExists(atPath: path)
    }
}

// MARK: - UserDefaults Extensions
extension UserDefaults {
    /// Convenience methods for app-specific settings
    var selectedModelID: String? {
        get { string(forKey: Constants.UserDefaultsKeys.selectedModelID) }
        set { set(newValue, forKey: Constants.UserDefaultsKeys.selectedModelID) }
    }
    
    var confidenceThreshold: Float {
        get { 
            let value = float(forKey: Constants.UserDefaultsKeys.confidenceThreshold)
            return value == 0 ? Constants.defaultConfidenceThreshold : value
        }
        set { set(newValue, forKey: Constants.UserDefaultsKeys.confidenceThreshold) }
    }
    
    var isDetectionEnabled: Bool {
        get { bool(forKey: Constants.UserDefaultsKeys.isDetectionEnabled) }
        set { set(newValue, forKey: Constants.UserDefaultsKeys.isDetectionEnabled) }
    }
    
    var frameProcessingInterval: Int {
        get { 
            let value = integer(forKey: Constants.UserDefaultsKeys.frameProcessingInterval)
            return value == 0 ? Constants.frameProcessingInterval : value
        }
        set { set(newValue, forKey: Constants.UserDefaultsKeys.frameProcessingInterval) }
    }
    
    var showConfidenceScores: Bool {
        get { bool(forKey: Constants.UserDefaultsKeys.showConfidenceScores) }
        set { set(newValue, forKey: Constants.UserDefaultsKeys.showConfidenceScores) }
    }
}

// MARK: - FileManager Extensions
extension FileManager {
    /// Returns the app's documents directory
    var documentsDirectory: URL {
        return urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    /// Returns the app's support directory
    var supportDirectory: URL {
        let url = urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        try? createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }
    
    /// Returns the models directory
    var modelsDirectory: URL {
        let url = supportDirectory.appendingPathComponent(Constants.modelsDirectoryName)
        try? createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }
    
    /// Returns available disk space in bytes
    var availableDiskSpace: Int64 {
        do {
            let systemAttributes = try attributesOfFileSystem(forPath: NSHomeDirectory())
            return (systemAttributes[.systemFreeSize] as? NSNumber)?.int64Value ?? 0
        } catch {
            return 0
        }
    }
}
