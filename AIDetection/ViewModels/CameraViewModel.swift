//
//  CameraViewModel.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation
import AVFoundation
import Combine
import SwiftUI

/// ViewModel for camera and detection functionality
class CameraViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isSessionRunning = false
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var currentDetections: [DetectionResult] = []
    @Published var isDetectionEnabled = true
    @Published var confidenceThreshold: Float = Constants.defaultConfidenceThreshold
    @Published var showConfidenceScores = true
    @Published var currentModel: MLModelInfo?
    @Published var isProcessing = false
    @Published var error: String?
    @Published var frameSize: CGSize = .zero
    
    // MARK: - Services
    private let cameraService = CameraService()
    private let detectionService = DetectionService()
    private let modelManager = ModelManagerService()
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupBindings()
        loadInitialModel()
    }
    
    // MARK: - Public Methods
    
    /// Starts the camera session
    func startCamera() {
        cameraService.startSession()
    }
    
    /// Stops the camera session
    func stopCamera() {
        cameraService.stopSession()
    }
    
    /// Requests camera permission
    func requestCameraPermission() {
        cameraService.requestCameraPermission()
    }
    
    /// Toggles detection on/off
    func toggleDetection() {
        isDetectionEnabled.toggle()
        detectionService.updateSettings(
            isEnabled: isDetectionEnabled,
            confidenceThreshold: confidenceThreshold
        )
        
        // Save to UserDefaults
        UserDefaults.standard.isDetectionEnabled = isDetectionEnabled
        
        // Clear detections if disabled
        if !isDetectionEnabled {
            currentDetections = []
        }
    }
    
    /// Updates confidence threshold
    func updateConfidenceThreshold(_ threshold: Float) {
        confidenceThreshold = threshold
        detectionService.updateSettings(
            isEnabled: isDetectionEnabled,
            confidenceThreshold: confidenceThreshold
        )
        
        // Save to UserDefaults
        UserDefaults.standard.confidenceThreshold = threshold
    }
    
    /// Toggles confidence score display
    func toggleConfidenceScores() {
        showConfidenceScores.toggle()
        UserDefaults.standard.showConfidenceScores = showConfidenceScores
    }
    
    /// Loads a new model for detection
    func loadModel(_ model: MLModelInfo) {
        Task {
            await detectionService.loadModel(model)
            await MainActor.run {
                self.currentModel = model
                self.modelManager.setSelectedModel(model)
            }
        }
    }
    
    /// Returns the camera capture session for preview
    func getCaptureSession() -> AVCaptureSession {
        return cameraService.getCaptureSession()
    }
    
    /// Clears current detections
    func clearDetections() {
        detectionService.clearDetections()
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Bind camera service properties
        cameraService.$isSessionRunning
            .receive(on: DispatchQueue.main)
            .assign(to: \.isSessionRunning, on: self)
            .store(in: &cancellables)
        
        cameraService.$cameraPermissionStatus
            .receive(on: DispatchQueue.main)
            .assign(to: \.cameraPermissionStatus, on: self)
            .store(in: &cancellables)
        
        cameraService.$error
            .receive(on: DispatchQueue.main)
            .map { $0?.localizedDescription }
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
        
        // Bind detection service properties
        detectionService.$currentDetections
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentDetections, on: self)
            .store(in: &cancellables)
        
        detectionService.$isDetectionEnabled
            .receive(on: DispatchQueue.main)
            .assign(to: \.isDetectionEnabled, on: self)
            .store(in: &cancellables)
        
        detectionService.$confidenceThreshold
            .receive(on: DispatchQueue.main)
            .assign(to: \.confidenceThreshold, on: self)
            .store(in: &cancellables)
        
        detectionService.$currentModel
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentModel, on: self)
            .store(in: &cancellables)
        
        detectionService.$isProcessing
            .receive(on: DispatchQueue.main)
            .assign(to: \.isProcessing, on: self)
            .store(in: &cancellables)
        
        detectionService.$error
            .receive(on: DispatchQueue.main)
            .map { $0?.localizedDescription }
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
        
        // Set camera service as frame delegate
        cameraService.frameDelegate = self
        
        // Load user preferences
        loadUserPreferences()
    }
    
    private func loadUserPreferences() {
        let userDefaults = UserDefaults.standard
        isDetectionEnabled = userDefaults.isDetectionEnabled
        confidenceThreshold = userDefaults.confidenceThreshold
        showConfidenceScores = userDefaults.showConfidenceScores
        
        // Update detection service with loaded preferences
        detectionService.updateSettings(
            isEnabled: isDetectionEnabled,
            confidenceThreshold: confidenceThreshold
        )
    }
    
    private func loadInitialModel() {
        Task {
            // Wait for model manager to load available models
            await modelManager.loadAvailableModels()
            
            await MainActor.run {
                // Try to load the previously selected model
                if let selectedModel = self.modelManager.getSelectedModel() {
                    self.loadModel(selectedModel)
                }
            }
        }
    }
}

// MARK: - CameraFrameDelegate
extension CameraViewModel: CameraFrameDelegate {
    
    func didReceiveFrame(_ sampleBuffer: CMSampleBuffer) {
        // Update frame size for UI calculations
        if let imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) {
            let width = CVPixelBufferGetWidth(imageBuffer)
            let height = CVPixelBufferGetHeight(imageBuffer)
            
            DispatchQueue.main.async {
                self.frameSize = CGSize(width: width, height: height)
            }
        }
        
        // Process frame for detection
        detectionService.processFrame(sampleBuffer)
    }
}
