//
//  SettingsViewModel.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine

/// ViewModel for app settings and preferences
class SettingsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var confidenceThreshold: Float = Constants.defaultConfidenceThreshold
    @Published var frameProcessingInterval: Int = Constants.frameProcessingInterval
    @Published var showConfidenceScores: Bool = true
    @Published var isDetectionEnabled: Bool = true
    @Published var maxDetectionsPerFrame: Int = Constants.maxDetectionsPerFrame
    
    // MARK: - App Info Properties
    @Published var appVersion: String = Constants.appVersion
    @Published var appName: String = Constants.appName
    
    // MARK: - Storage Info
    @Published var availableStorage: String = ""
    @Published var usedModelStorage: String = ""
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let fileManager = FileManager.default
    
    // MARK: - Initialization
    init() {
        loadSettings()
        updateStorageInfo()
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    /// Saves current settings to UserDefaults
    func saveSettings() {
        let userDefaults = UserDefaults.standard
        userDefaults.confidenceThreshold = confidenceThreshold
        userDefaults.frameProcessingInterval = frameProcessingInterval
        userDefaults.showConfidenceScores = showConfidenceScores
        userDefaults.isDetectionEnabled = isDetectionEnabled
    }
    
    /// Resets settings to default values
    func resetToDefaults() {
        confidenceThreshold = Constants.defaultConfidenceThreshold
        frameProcessingInterval = Constants.frameProcessingInterval
        showConfidenceScores = true
        isDetectionEnabled = true
        maxDetectionsPerFrame = Constants.maxDetectionsPerFrame
        
        saveSettings()
    }
    
    /// Updates storage information
    func updateStorageInfo() {
        Task {
            let available = await getAvailableStorageString()
            let used = await getUsedModelStorageString()
            
            await MainActor.run {
                self.availableStorage = available
                self.usedModelStorage = used
            }
        }
    }
    
    /// Clears all downloaded models
    func clearAllDownloadedModels() {
        let modelsDirectory = fileManager.modelsDirectory
        
        do {
            let contents = try fileManager.contentsOfDirectory(at: modelsDirectory, includingPropertiesForKeys: nil)
            
            for fileURL in contents {
                try fileManager.removeItem(at: fileURL)
            }
            
            updateStorageInfo()
        } catch {
            print("Failed to clear downloaded models: \(error)")
        }
    }
    
    /// Returns confidence threshold as percentage string
    var confidenceThresholdPercentage: String {
        return String(format: "%.0f%%", confidenceThreshold * 100)
    }
    
    /// Returns frame processing interval description
    var frameProcessingDescription: String {
        switch frameProcessingInterval {
        case 1:
            return "Every frame (High CPU usage)"
        case 2...5:
            return "Every \(frameProcessingInterval) frames (Balanced)"
        case 6...10:
            return "Every \(frameProcessingInterval) frames (Low CPU usage)"
        default:
            return "Every \(frameProcessingInterval) frames"
        }
    }
    
    /// Returns max detections description
    var maxDetectionsDescription: String {
        return "Show up to \(maxDetectionsPerFrame) detections per frame"
    }
    
    // MARK: - Private Methods
    
    private func loadSettings() {
        let userDefaults = UserDefaults.standard
        confidenceThreshold = userDefaults.confidenceThreshold
        frameProcessingInterval = userDefaults.frameProcessingInterval
        showConfidenceScores = userDefaults.showConfidenceScores
        isDetectionEnabled = userDefaults.isDetectionEnabled
    }
    
    private func setupBindings() {
        // Auto-save when settings change
        Publishers.CombineLatest4(
            $confidenceThreshold,
            $frameProcessingInterval,
            $showConfidenceScores,
            $isDetectionEnabled
        )
        .debounce(for: .milliseconds(500), scheduler: DispatchQueue.main)
        .sink { [weak self] _, _, _, _ in
            self?.saveSettings()
        }
        .store(in: &cancellables)
    }
    
    private func getAvailableStorageString() async -> String {
        let availableBytes = fileManager.availableDiskSpace
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useGB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: availableBytes)
    }
    
    private func getUsedModelStorageString() async -> String {
        let modelsDirectory = fileManager.modelsDirectory
        var totalSize: Int64 = 0
        
        do {
            let contents = try fileManager.contentsOfDirectory(at: modelsDirectory, includingPropertiesForKeys: [.fileSizeKey])
            
            for fileURL in contents {
                totalSize += fileURL.fileSize
            }
        } catch {
            print("Failed to calculate model storage: \(error)")
        }
        
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useGB, .useMB, .useKB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: totalSize)
    }
}

// MARK: - Settings Sections
extension SettingsViewModel {
    
    /// Detection settings section
    var detectionSettings: [(String, String, Any)] {
        return [
            ("Detection Enabled", "Enable/disable object detection", isDetectionEnabled),
            ("Confidence Threshold", confidenceThresholdPercentage, confidenceThreshold),
            ("Show Confidence Scores", "Display confidence percentages", showConfidenceScores),
            ("Processing Interval", frameProcessingDescription, frameProcessingInterval),
            ("Max Detections", maxDetectionsDescription, maxDetectionsPerFrame)
        ]
    }
    
    /// App info section
    var appInfo: [(String, String)] {
        return [
            ("App Name", appName),
            ("Version", appVersion),
            ("Available Storage", availableStorage),
            ("Model Storage Used", usedModelStorage)
        ]
    }
}
