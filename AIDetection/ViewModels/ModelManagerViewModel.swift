//
//  ModelManagerViewModel.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine

/// ViewModel for managing CoreML models
class ModelManagerViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var availableModels: [MLModelInfo] = []
    @Published var downloadProgress: [String: Double] = [:]
    @Published var isLoading = false
    @Published var error: String?
    @Published var selectedModel: MLModelInfo?
    @Published var showingAddModelSheet = false
    @Published var newModelURL = ""
    @Published var newModelName = ""
    @Published var newModelDescription = ""
    
    // MARK: - Services
    private let modelManager = ModelManagerService()
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupBindings()
        loadModels()
    }
    
    // MARK: - Public Methods
    
    /// Refreshes the list of available models
    func refreshModels() {
        modelManager.loadAvailableModels()
    }
    
    /// Downloads a new model from URL
    func downloadModel() {
        guard !newModelURL.isEmpty, !newModelName.isEmpty else {
            error = "Please provide both URL and name for the model."
            return
        }
        
        Task {
            await modelManager.downloadModel(
                from: newModelURL,
                name: newModelName,
                description: newModelDescription
            )
            
            await MainActor.run {
                self.clearNewModelFields()
                self.showingAddModelSheet = false
            }
        }
    }
    
    /// Cancels a model download
    func cancelDownload(for modelID: String) {
        modelManager.cancelDownload(for: modelID)
    }
    
    /// Deletes a downloaded model
    func deleteModel(_ model: MLModelInfo) {
        modelManager.deleteModel(model)
    }
    
    /// Selects a model for detection
    func selectModel(_ model: MLModelInfo) {
        selectedModel = model
        modelManager.setSelectedModel(model)
    }
    
    /// Shows the add model sheet
    func showAddModelSheet() {
        clearNewModelFields()
        showingAddModelSheet = true
    }
    
    /// Hides the add model sheet
    func hideAddModelSheet() {
        showingAddModelSheet = false
        clearNewModelFields()
    }
    
    /// Adds a sample model for testing
    func addSampleModel() {
        newModelURL = Constants.SampleModels.yoloV3URL
        newModelName = "YOLOv3"
        newModelDescription = "Real-time object detection model"
    }
    
    /// Returns whether a model is currently downloading
    func isDownloading(_ model: MLModelInfo) -> Bool {
        return downloadProgress.keys.contains(model.id)
    }
    
    /// Returns download progress for a model
    func getDownloadProgress(for model: MLModelInfo) -> Double {
        return downloadProgress[model.id] ?? 0.0
    }
    
    /// Returns formatted download progress string
    func getDownloadProgressString(for model: MLModelInfo) -> String {
        let progress = getDownloadProgress(for: model)
        return String(format: "%.0f%%", progress * 100)
    }
    
    /// Groups models by type
    func groupedModels() -> [MLModelInfo.ModelType: [MLModelInfo]] {
        return Dictionary(grouping: availableModels) { $0.modelType }
    }
    
    /// Returns bundled models
    var bundledModels: [MLModelInfo] {
        return availableModels.filter { $0.isBundled }
    }
    
    /// Returns downloaded models
    var downloadedModels: [MLModelInfo] {
        return availableModels.filter { !$0.isBundled }
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Bind model manager properties
        modelManager.$availableModels
            .receive(on: DispatchQueue.main)
            .assign(to: \.availableModels, on: self)
            .store(in: &cancellables)
        
        modelManager.$downloadProgress
            .receive(on: DispatchQueue.main)
            .assign(to: \.downloadProgress, on: self)
            .store(in: &cancellables)
        
        modelManager.$isLoading
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        modelManager.$error
            .receive(on: DispatchQueue.main)
            .map { $0?.localizedDescription }
            .assign(to: \.error, on: self)
            .store(in: &cancellables)
        
        // Load selected model
        loadSelectedModel()
    }
    
    private func loadModels() {
        modelManager.loadAvailableModels()
    }
    
    private func loadSelectedModel() {
        selectedModel = modelManager.getSelectedModel()
    }
    
    private func clearNewModelFields() {
        newModelURL = ""
        newModelName = ""
        newModelDescription = ""
    }
}
