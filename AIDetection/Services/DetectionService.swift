//
//  DetectionService.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Vision
import CoreML
import AVFoundation
import UIKit
import Combine

/// Handles object detection using Vision and CoreML
class DetectionService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentDetections: [DetectionResult] = []
    @Published var isDetectionEnabled = true
    @Published var confidenceThreshold: Float = Constants.defaultConfidenceThreshold
    @Published var currentModel: MLModelInfo?
    @Published var isProcessing = false
    @Published var error: DetectionError?
    
    // MARK: - Private Properties
    private var visionModel: VNCoreMLModel?
    private var frameCounter = 0
    private let processingQueue = DispatchQueue(label: "detection.processing.queue", qos: .userInitiated)
    private var lastProcessingTime = Date()
    
    // MARK: - Initialization
    init() {
        setupDefaultSettings()
    }
    
    // MARK: - Public Methods
    
    /// Loads a CoreML model for detection
    func loadModel(_ modelInfo: MLModelInfo) async {
        await MainActor.run {
            isProcessing = true
            error = nil
        }
        
        do {
            guard let modelURL = modelInfo.modelURL else {
                throw DetectionError.modelNotFound
            }
            
            let mlModel = try MLModel(contentsOf: modelURL)
            let visionModel = try VNCoreMLModel(for: mlModel)
            
            await MainActor.run {
                self.visionModel = visionModel
                self.currentModel = modelInfo
                self.isProcessing = false
            }
            
            print("Successfully loaded model: \(modelInfo.name)")
            
        } catch {
            await MainActor.run {
                self.error = .modelLoadingFailed(error.localizedDescription)
                self.isProcessing = false
            }
            print("Failed to load model: \(error)")
        }
    }
    
    /// Processes a video frame for object detection
    func processFrame(_ sampleBuffer: CMSampleBuffer) {
        guard isDetectionEnabled,
              let visionModel = visionModel,
              shouldProcessFrame() else {
            return
        }
        
        guard let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else {
            return
        }
        
        processingQueue.async { [weak self] in
            self?.performDetection(on: pixelBuffer, using: visionModel)
        }
    }
    
    /// Updates detection settings
    func updateSettings(isEnabled: Bool, confidenceThreshold: Float) {
        DispatchQueue.main.async {
            self.isDetectionEnabled = isEnabled
            self.confidenceThreshold = confidenceThreshold
            
            // Clear detections if disabled
            if !isEnabled {
                self.currentDetections = []
            }
        }
    }
    
    /// Clears current detections
    func clearDetections() {
        DispatchQueue.main.async {
            self.currentDetections = []
        }
    }
    
    // MARK: - Private Methods
    
    private func setupDefaultSettings() {
        let userDefaults = UserDefaults.standard
        isDetectionEnabled = userDefaults.isDetectionEnabled
        confidenceThreshold = userDefaults.confidenceThreshold
    }
    
    private func shouldProcessFrame() -> Bool {
        frameCounter += 1
        
        // Process every nth frame based on settings
        let interval = UserDefaults.standard.frameProcessingInterval
        guard frameCounter % interval == 0 else {
            return false
        }
        
        // Throttle processing to avoid overwhelming the system
        let now = Date()
        let timeSinceLastProcessing = now.timeIntervalSince(lastProcessingTime)
        guard timeSinceLastProcessing >= 0.1 else { // Minimum 100ms between processing
            return false
        }
        
        lastProcessingTime = now
        return true
    }
    
    private func performDetection(on pixelBuffer: CVPixelBuffer, using model: VNCoreMLModel) {
        let request = VNCoreMLRequest(model: model) { [weak self] request, error in
            self?.handleDetectionResults(request: request, error: error)
        }
        
        // Configure request
        request.imageCropAndScaleOption = .scaleFill
        
        let handler = VNImageRequestHandler(cvPixelBuffer: pixelBuffer, orientation: .up, options: [:])
        
        do {
            try handler.perform([request])
        } catch {
            DispatchQueue.main.async {
                self.error = .processingFailed(error.localizedDescription)
            }
        }
    }
    
    private func handleDetectionResults(request: VNRequest, error: Error?) {
        if let error = error {
            DispatchQueue.main.async {
                self.error = .processingFailed(error.localizedDescription)
            }
            return
        }
        
        guard let observations = request.results as? [VNRecognizedObjectObservation] else {
            return
        }
        
        // Filter observations by confidence threshold
        let filteredObservations = observations.filter { $0.confidence >= confidenceThreshold }
        
        // Convert to DetectionResult objects
        let detections = filteredObservations.map { DetectionResult(from: $0) }
        
        // Limit number of detections to avoid UI clutter
        let limitedDetections = Array(detections.prefix(Constants.maxDetectionsPerFrame))
        
        DispatchQueue.main.async {
            self.currentDetections = limitedDetections
        }
    }
}

// MARK: - Supporting Types

/// Detection-related errors
enum DetectionError: LocalizedError {
    case modelNotFound
    case modelLoadingFailed(String)
    case processingFailed(String)
    case invalidModelFormat
    
    var errorDescription: String? {
        switch self {
        case .modelNotFound:
            return "Model file not found."
        case .modelLoadingFailed(let message):
            return "Failed to load model: \(message)"
        case .processingFailed(let message):
            return "Detection processing failed: \(message)"
        case .invalidModelFormat:
            return Constants.ErrorMessages.invalidModelFormat
        }
    }
}
