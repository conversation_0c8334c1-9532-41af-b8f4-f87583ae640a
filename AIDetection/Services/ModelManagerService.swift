//
//  ModelManagerService.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation
import CoreML
import Combine

/// Manages CoreML model downloads, storage, and metadata
class ModelManagerService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var availableModels: [MLModelInfo] = []
    @Published var downloadProgress: [String: Double] = [:]
    @Published var isLoading = false
    @Published var error: ModelManagerError?
    
    // MARK: - Private Properties
    private let fileManager = FileManager.default
    private var downloadTasks: [String: URLSessionDownloadTask] = [:]
    private lazy var urlSession: URLSession = {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = Constants.downloadTimeoutInterval
        return URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }()
    
    // MARK: - Initialization
    init() {
        loadAvailableModels()
    }
    
    // MARK: - Public Methods
    
    /// Loads all available models (bundled + downloaded)
    func loadAvailableModels() {
        isLoading = true
        
        Task {
            var models: [MLModelInfo] = []
            
            // Load bundled models
            models.append(contentsOf: await loadBundledModels())
            
            // Load downloaded models
            models.append(contentsOf: await loadDownloadedModels())
            
            await MainActor.run {
                self.availableModels = models.sorted { $0.name < $1.name }
                self.isLoading = false
            }
        }
    }
    
    /// Downloads a model from URL
    func downloadModel(from url: String, name: String, description: String = "") async {
        guard let downloadURL = URL(string: url) else {
            await MainActor.run {
                self.error = .invalidURL
            }
            return
        }
        
        let modelID = UUID().uuidString
        
        // Check available storage
        let estimatedSize = await getEstimatedFileSize(from: downloadURL)
        if estimatedSize > 0 && !hasEnoughStorage(for: estimatedSize) {
            await MainActor.run {
                self.error = .insufficientStorage
            }
            return
        }
        
        await MainActor.run {
            self.downloadProgress[modelID] = 0.0
        }
        
        let downloadTask = urlSession.downloadTask(with: downloadURL) { [weak self] localURL, response, error in
            Task {
                await self?.handleDownloadCompletion(
                    modelID: modelID,
                    localURL: localURL,
                    response: response,
                    error: error,
                    name: name,
                    description: description,
                    originalURL: url
                )
            }
        }
        
        downloadTasks[modelID] = downloadTask
        downloadTask.resume()
    }
    
    /// Cancels a model download
    func cancelDownload(for modelID: String) {
        downloadTasks[modelID]?.cancel()
        downloadTasks.removeValue(forKey: modelID)
        
        DispatchQueue.main.async {
            self.downloadProgress.removeValue(forKey: modelID)
        }
    }
    
    /// Deletes a downloaded model
    func deleteModel(_ model: MLModelInfo) {
        guard !model.isBundled,
              let filePath = model.filePath else {
            return
        }
        
        let fileURL = URL(fileURLWithPath: filePath)
        
        do {
            try fileManager.removeItem(at: fileURL)
            
            DispatchQueue.main.async {
                self.availableModels.removeAll { $0.id == model.id }
            }
        } catch {
            DispatchQueue.main.async {
                self.error = .deletionFailed(error.localizedDescription)
            }
        }
    }
    
    /// Returns the selected model from UserDefaults
    func getSelectedModel() -> MLModelInfo? {
        guard let selectedID = UserDefaults.standard.selectedModelID else {
            return availableModels.first
        }
        
        return availableModels.first { $0.id == selectedID }
    }
    
    /// Sets the selected model in UserDefaults
    func setSelectedModel(_ model: MLModelInfo) {
        UserDefaults.standard.selectedModelID = model.id
    }
    
    // MARK: - Private Methods
    
    private func loadBundledModels() async -> [MLModelInfo] {
        guard let bundlePath = Bundle.main.path(forResource: "BundledModels", ofType: nil) else {
            return []
        }
        
        let bundleURL = URL(fileURLWithPath: bundlePath)
        return await loadModelsFromDirectory(bundleURL, isBundled: true)
    }
    
    private func loadDownloadedModels() async -> [MLModelInfo] {
        let modelsDirectory = fileManager.modelsDirectory
        return await loadModelsFromDirectory(modelsDirectory, isBundled: false)
    }
    
    private func loadModelsFromDirectory(_ directory: URL, isBundled: Bool) async -> [MLModelInfo] {
        var models: [MLModelInfo] = []
        
        do {
            let contents = try fileManager.contentsOfDirectory(at: directory, includingPropertiesForKeys: nil)
            
            for fileURL in contents {
                if fileURL.pathExtension == Constants.modelFileExtension ||
                   fileURL.pathExtension == Constants.compiledModelExtension {
                    
                    if let modelInfo = MLModelInfo.from(modelURL: fileURL, isBundled: isBundled) {
                        models.append(modelInfo)
                    }
                }
            }
        } catch {
            print("Failed to load models from directory \(directory): \(error)")
        }
        
        return models
    }
    
    private func getEstimatedFileSize(from url: URL) async -> Int64 {
        do {
            var request = URLRequest(url: url)
            request.httpMethod = "HEAD"
            
            let (_, response) = try await urlSession.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse,
               let contentLength = httpResponse.value(forHTTPHeaderField: "Content-Length"),
               let size = Int64(contentLength) {
                return size
            }
        } catch {
            print("Failed to get file size: \(error)")
        }
        
        return 0
    }
    
    private func hasEnoughStorage(for fileSize: Int64) -> Bool {
        let availableSpace = fileManager.availableDiskSpace
        let requiredSpace = fileSize + (100 * 1024 * 1024) // Add 100MB buffer
        return availableSpace >= requiredSpace
    }
    
    private func handleDownloadCompletion(
        modelID: String,
        localURL: URL?,
        response: URLResponse?,
        error: Error?,
        name: String,
        description: String,
        originalURL: String
    ) async {
        defer {
            await MainActor.run {
                self.downloadProgress.removeValue(forKey: modelID)
                self.downloadTasks.removeValue(forKey: modelID)
            }
        }
        
        if let error = error {
            await MainActor.run {
                self.error = .downloadFailed(error.localizedDescription)
            }
            return
        }
        
        guard let localURL = localURL else {
            await MainActor.run {
                self.error = .downloadFailed("No file received")
            }
            return
        }
        
        // Move file to models directory
        let modelsDirectory = fileManager.modelsDirectory
        let fileName = "\(name.replacingOccurrences(of: " ", with: "_")).mlmodel"
        let destinationURL = modelsDirectory.appendingPathComponent(fileName)
        
        do {
            // Remove existing file if it exists
            if fileManager.fileExists(atPath: destinationURL.path) {
                try fileManager.removeItem(at: destinationURL)
            }
            
            try fileManager.moveItem(at: localURL, to: destinationURL)
            
            // Create model info
            if let modelInfo = MLModelInfo.from(modelURL: destinationURL, isBundled: false) {
                await MainActor.run {
                    self.availableModels.append(modelInfo)
                    self.availableModels.sort { $0.name < $1.name }
                }
            }
            
        } catch {
            await MainActor.run {
                self.error = .downloadFailed("Failed to save model: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - URLSessionDownloadDelegate
extension ModelManagerService: URLSessionDownloadDelegate {
    
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didWriteData bytesWritten: Int64, totalBytesWritten: Int64, totalBytesExpectedToWrite: Int64) {
        
        // Find the model ID for this task
        let modelID = downloadTasks.first { $0.value == downloadTask }?.key
        
        guard let modelID = modelID, totalBytesExpectedToWrite > 0 else { return }
        
        let progress = Double(totalBytesWritten) / Double(totalBytesExpectedToWrite)
        
        DispatchQueue.main.async {
            self.downloadProgress[modelID] = progress
        }
    }
    
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
        // Handled in completion handler
    }
}

// MARK: - Supporting Types

/// Model manager related errors
enum ModelManagerError: LocalizedError {
    case invalidURL
    case downloadFailed(String)
    case deletionFailed(String)
    case insufficientStorage
    case modelValidationFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid model download URL."
        case .downloadFailed(let message):
            return "Download failed: \(message)"
        case .deletionFailed(let message):
            return "Failed to delete model: \(message)"
        case .insufficientStorage:
            return Constants.ErrorMessages.insufficientStorage
        case .modelValidationFailed:
            return "Model validation failed."
        }
    }
}
