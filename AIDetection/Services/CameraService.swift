//
//  CameraService.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import AVFoundation
import UIKit
import Combine

/// Manages camera capture session and video output
class CameraService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isSessionRunning = false
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var error: CameraError?
    
    // MARK: - Private Properties
    private let captureSession = AVCaptureSession()
    private var videoOutput: AVCaptureVideoDataOutput?
    private var captureDevice: AVCaptureDevice?
    private let sessionQueue = DispatchQueue(label: "camera.session.queue")
    private let videoOutputQueue = DispatchQueue(label: "camera.video.output.queue")
    
    // MARK: - Delegates
    weak var frameDelegate: CameraFrameDelegate?
    
    // MARK: - Initialization
    override init() {
        super.init()
        checkCameraPermission()
    }
    
    // MARK: - Public Methods
    
    /// Starts the camera capture session
    func startSession() {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if self.cameraPermissionStatus == .authorized {
                self.setupCaptureSession()
                self.captureSession.startRunning()
                
                DispatchQueue.main.async {
                    self.isSessionRunning = self.captureSession.isRunning
                }
            } else {
                DispatchQueue.main.async {
                    self.error = .permissionDenied
                }
            }
        }
    }
    
    /// Stops the camera capture session
    func stopSession() {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if self.captureSession.isRunning {
                self.captureSession.stopRunning()
                
                DispatchQueue.main.async {
                    self.isSessionRunning = false
                }
            }
        }
    }
    
    /// Requests camera permission
    func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.cameraPermissionStatus = granted ? .authorized : .denied
                if granted {
                    self?.startSession()
                } else {
                    self?.error = .permissionDenied
                }
            }
        }
    }
    
    /// Returns the capture session for preview layer
    func getCaptureSession() -> AVCaptureSession {
        return captureSession
    }
    
    // MARK: - Private Methods
    
    private func checkCameraPermission() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
    }
    
    private func setupCaptureSession() {
        captureSession.beginConfiguration()
        
        // Configure session preset
        if captureSession.canSetSessionPreset(.high) {
            captureSession.sessionPreset = .high
        }
        
        // Setup camera input
        setupCameraInput()
        
        // Setup video output
        setupVideoOutput()
        
        captureSession.commitConfiguration()
    }
    
    private func setupCameraInput() {
        // Remove existing inputs
        captureSession.inputs.forEach { captureSession.removeInput($0) }
        
        // Get back camera
        guard let camera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            DispatchQueue.main.async {
                self.error = .cameraNotAvailable
            }
            return
        }
        
        self.captureDevice = camera
        
        do {
            let cameraInput = try AVCaptureDeviceInput(device: camera)
            
            if captureSession.canAddInput(cameraInput) {
                captureSession.addInput(cameraInput)
            } else {
                DispatchQueue.main.async {
                    self.error = .configurationFailed
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.error = .configurationFailed
            }
        }
    }
    
    private func setupVideoOutput() {
        // Remove existing outputs
        captureSession.outputs.forEach { captureSession.removeOutput($0) }
        
        let videoOutput = AVCaptureVideoDataOutput()
        videoOutput.setSampleBufferDelegate(self, queue: videoOutputQueue)
        videoOutput.alwaysDiscardsLateVideoFrames = true
        
        // Configure video settings
        videoOutput.videoSettings = [
            kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA
        ]
        
        if captureSession.canAddOutput(videoOutput) {
            captureSession.addOutput(videoOutput)
            self.videoOutput = videoOutput
            
            // Configure video orientation
            if let connection = videoOutput.connection(with: .video) {
                if connection.isVideoOrientationSupported {
                    connection.videoOrientation = .portrait
                }
                if connection.isVideoMirroringSupported {
                    connection.isVideoMirrored = false
                }
            }
        } else {
            DispatchQueue.main.async {
                self.error = .configurationFailed
            }
        }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension CameraService: AVCaptureVideoDataOutputSampleBufferDelegate {
    
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        // Forward frame to delegate for processing
        frameDelegate?.didReceiveFrame(sampleBuffer)
    }
    
    func captureOutput(_ output: AVCaptureOutput, didDrop sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        // Handle dropped frames if needed
        print("Dropped frame")
    }
}

// MARK: - Supporting Types

/// Delegate protocol for receiving camera frames
protocol CameraFrameDelegate: AnyObject {
    func didReceiveFrame(_ sampleBuffer: CMSampleBuffer)
}

/// Camera-related errors
enum CameraError: LocalizedError {
    case permissionDenied
    case cameraNotAvailable
    case configurationFailed
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return Constants.ErrorMessages.cameraPermissionDenied
        case .cameraNotAvailable:
            return Constants.ErrorMessages.cameraNotAvailable
        case .configurationFailed:
            return "Failed to configure camera session."
        }
    }
}
