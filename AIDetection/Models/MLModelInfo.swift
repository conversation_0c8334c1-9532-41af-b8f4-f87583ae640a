//
//  MLModelInfo.swift
//  AIDetection
//
//  Created by AI Assistant on 2024.
//

import Foundation
import CoreML

/// Represents information about a CoreML model
struct MLModelInfo: Identifiable, Codable, Equatable {
    let id: String
    let name: String
    let description: String
    let version: String
    let filePath: String?
    let downloadURL: String?
    let fileSize: Int64
    let isDownloaded: Bool
    let isBundled: Bool
    let modelType: ModelType
    let supportedClasses: [String]
    let inputImageSize: ImageSize?
    let createdDate: Date
    let lastModified: Date
    
    enum ModelType: String, Codable, CaseIterable {
        case objectDetection = "Object Detection"
        case imageClassification = "Image Classification"
        case segmentation = "Segmentation"
        case unknown = "Unknown"
        
        var icon: String {
            switch self {
            case .objectDetection:
                return "viewfinder"
            case .imageClassification:
                return "photo"
            case .segmentation:
                return "square.split.2x2"
            case .unknown:
                return "questionmark.circle"
            }
        }
    }
    
    struct ImageSize: Codable, Equatable {
        let width: Int
        let height: Int
        
        var description: String {
            return "\(width)×\(height)"
        }
    }
    
    init(
        id: String = UUID().uuidString,
        name: String,
        description: String = "",
        version: String = "1.0",
        filePath: String? = nil,
        downloadURL: String? = nil,
        fileSize: Int64 = 0,
        isDownloaded: Bool = false,
        isBundled: Bool = false,
        modelType: ModelType = .unknown,
        supportedClasses: [String] = [],
        inputImageSize: ImageSize? = nil
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.version = version
        self.filePath = filePath
        self.downloadURL = downloadURL
        self.fileSize = fileSize
        self.isDownloaded = isDownloaded
        self.isBundled = isBundled
        self.modelType = modelType
        self.supportedClasses = supportedClasses
        self.inputImageSize = inputImageSize
        self.createdDate = Date()
        self.lastModified = Date()
    }
    
    /// Creates MLModelInfo from a CoreML model file
    static func from(modelURL: URL, isBundled: Bool = false) -> MLModelInfo? {
        do {
            let model = try MLModel(contentsOf: modelURL)
            let modelDescription = model.modelDescription
            
            // Extract model metadata
            let name = modelDescription.metadata[.shortDescription] as? String ?? modelURL.lastPathComponent
            let description = modelDescription.metadata[.description] as? String ?? ""
            let version = modelDescription.metadata[.versionString] as? String ?? "1.0"
            
            // Determine model type based on output features
            let modelType = determineModelType(from: modelDescription)
            
            // Extract supported classes if available
            let supportedClasses = extractSupportedClasses(from: modelDescription)
            
            // Extract input image size
            let inputImageSize = extractInputImageSize(from: modelDescription)
            
            // Get file size
            let fileSize = try modelURL.resourceValues(forKeys: [.fileSizeKey]).fileSize ?? 0
            
            return MLModelInfo(
                name: name,
                description: description,
                version: version,
                filePath: modelURL.path,
                fileSize: Int64(fileSize),
                isDownloaded: true,
                isBundled: isBundled,
                modelType: modelType,
                supportedClasses: supportedClasses,
                inputImageSize: inputImageSize
            )
        } catch {
            print("Failed to load model info from \(modelURL): \(error)")
            return nil
        }
    }
    
    /// Determines the model type based on output features
    private static func determineModelType(from description: MLModelDescription) -> ModelType {
        let outputNames = description.outputFeatureNames
        
        // Check for common object detection output patterns
        if outputNames.contains(where: { $0.lowercased().contains("coordinates") || $0.lowercased().contains("boxes") }) {
            return .objectDetection
        }
        
        // Check for classification patterns
        if outputNames.contains(where: { $0.lowercased().contains("class") || $0.lowercased().contains("label") }) {
            return .imageClassification
        }
        
        // Check for segmentation patterns
        if outputNames.contains(where: { $0.lowercased().contains("mask") || $0.lowercased().contains("segment") }) {
            return .segmentation
        }
        
        return .unknown
    }
    
    /// Extracts supported classes from model metadata
    private static func extractSupportedClasses(from description: MLModelDescription) -> [String] {
        // Try to extract class labels from metadata
        if let classLabels = description.metadata[.creatorDefinedKey] as? [String] {
            return classLabels
        }
        
        // Check output feature descriptions for class labels
        for feature in description.outputFeatureNames {
            if let outputFeature = description.outputDescriptionsByName[feature],
               let constraint = outputFeature.multiArrayConstraint,
               let shapeConstraint = constraint.shapeConstraint as? [NSNumber],
               shapeConstraint.count > 0 {
                // For classification models, the number of classes might be in the shape
                let numClasses = shapeConstraint.last?.intValue ?? 0
                if numClasses > 0 && numClasses < 10000 { // Reasonable upper bound
                    return (0..<numClasses).map { "Class \($0)" }
                }
            }
        }
        
        return []
    }
    
    /// Extracts input image size from model description
    private static func extractInputImageSize(from description: MLModelDescription) -> ImageSize? {
        for feature in description.inputFeatureNames {
            if let inputFeature = description.inputDescriptionsByName[feature],
               let imageConstraint = inputFeature.imageConstraint {
                return ImageSize(
                    width: Int(imageConstraint.pixelsWide),
                    height: Int(imageConstraint.pixelsHigh)
                )
            }
        }
        return nil
    }
    
    /// Returns formatted file size string
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    /// Returns the model file URL if available
    var modelURL: URL? {
        guard let filePath = filePath else { return nil }
        return URL(fileURLWithPath: filePath)
    }
    
    /// Returns whether the model is ready to use
    var isReadyToUse: Bool {
        return isDownloaded && filePath != nil
    }
    
    static func == (lhs: MLModelInfo, rhs: MLModelInfo) -> Bool {
        return lhs.id == rhs.id
    }
}
