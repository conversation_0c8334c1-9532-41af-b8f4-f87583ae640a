{"builtTestProducts": [], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.AIDetection-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources", "importPath": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources"}], "isLibrary": true, "moduleName": "AIDetection", "moduleOutputPath": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules/AIDetection.swiftmodule", "objects": ["/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.swift.o", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetection-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "aidetection"], "outputFileMapPath": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules/AIDetection.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift"], "tempsPath": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"AIDetection": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "AIDetection", "-package-name", "aidetection", "-incremental", "-c", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift", "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift", "-I", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetection-Swift.h", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "aidetection", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"AIDetection": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift"}, {"kind": "file", "name": "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift"}], "outputFilePath": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources"}, "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}