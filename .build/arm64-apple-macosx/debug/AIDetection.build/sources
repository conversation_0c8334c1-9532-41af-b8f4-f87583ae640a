'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift'
'/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift'
