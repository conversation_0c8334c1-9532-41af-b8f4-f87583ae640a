{"": {"swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/master.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.swiftdeps"}, "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift": {"dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.d", "object": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.swift.o", "swiftmodule": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.swiftdeps"}}