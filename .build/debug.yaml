client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "AIDetection-arm64-apple-macosx15.0-debug.module": ["<AIDetection-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<AIDetection-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<AIDetection-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/project/iOS AI/AIDetection/AIDetection/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift"]
    outputs: ["/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources"]
    description: "Write auxiliary file /Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources"

  "/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<AIDetection-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules/AIDetection.swiftmodule"]
    outputs: ["<AIDetection-arm64-apple-macosx15.0-debug.module>"]

  "C.AIDetection-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/project/iOS AI/AIDetection/AIDetection/App/AIDetectionApp.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/DetectionResult.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Models/MLModelInfo.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/CameraService.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/DetectionService.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Services/ModelManagerService.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Constants.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Utilities/Extensions.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/CameraViewModel.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/ModelManagerViewModel.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/ViewModels/SettingsViewModel.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/CameraView.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ContentView.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/ModelListView.swift","/Users/<USER>/project/iOS AI/AIDetection/AIDetection/Views/SettingsView.swift","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources"]
    outputs: ["/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetectionApp.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionResult.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/MLModelInfo.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraService.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/DetectionService.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerService.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Constants.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/Extensions.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraViewModel.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelManagerViewModel.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsViewModel.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/CameraView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ContentView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/ModelListView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/SettingsView.swift.o","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules/AIDetection.swiftmodule"]
    description: "Compiling Swift Module 'AIDetection' (15 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","AIDetection","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules/AIDetection.swiftmodule","-output-file-map","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/sources","-I","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/project/iOS AI/AIDetection/.build/arm64-apple-macosx/debug/AIDetection.build/AIDetection-Swift.h","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","aidetection"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/project/iOS AI/AIDetection/AIDetection/","/Users/<USER>/project/iOS AI/AIDetection/Package.swift","/Users/<USER>/project/iOS AI/AIDetection/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

