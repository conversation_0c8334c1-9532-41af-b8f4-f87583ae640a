# AI Detection iOS App

A complete SwiftUI iOS application with real-time AI object detection capabilities using Apple's Vision framework and CoreML.

## Features

### 🎥 Real-time Camera Detection
- Live camera feed with AVCaptureSession
- Real-time object detection using Vision framework
- Customizable detection overlays with bounding boxes
- Confidence scores and class labels
- Toggle detection on/off without stopping camera

### 🧠 Dynamic Model Management
- Support for both bundled and downloadable CoreML models
- Model metadata extraction and validation
- Download progress tracking with cancel functionality
- Persistent model storage in app support directory
- Model selection and dynamic loading without app restart

### ⚙️ Advanced Settings
- Confidence threshold slider (10% - 100%)
- Frame processing interval control for performance optimization
- Toggle confidence score display
- Maximum detections per frame limit
- Storage usage monitoring

### 📱 Modern SwiftUI Interface
- Clean, intuitive tab-based navigation
- MVVM architecture with ObservableObject
- Reactive UI updates with Combine
- Error handling with user-friendly alerts
- Dark/Light mode support

## Technical Implementation

### Architecture
- **MVVM Pattern**: Separation of concerns with ViewModels
- **Services Layer**: Dedicated services for camera, detection, and model management
- **Reactive Programming**: Combine framework for data binding
- **Async/Await**: Modern concurrency for model operations

### Key Components

#### Services
- `CameraService`: AVCaptureSession management and frame delegation
- `DetectionService`: Vision/CoreML integration and result processing
- `ModelManagerService`: Model download, storage, and metadata management

#### ViewModels
- `CameraViewModel`: Camera and detection state management
- `ModelManagerViewModel`: Model list and download operations
- `SettingsViewModel`: App preferences and configuration

#### Views
- `CameraView`: Live camera preview with detection overlays
- `ModelListView`: Model management interface with download progress
- `SettingsView`: Comprehensive settings and preferences

## Requirements

- iOS 17.0+
- Xcode 15.0+
- Swift 5.9+
- Device with camera (real device required for testing)

## Installation

1. **Clone or download the project**
2. **Open `AIDetection.xcodeproj` in Xcode**
3. **Select your development team** in project settings
4. **Build and run** on a physical iOS device (camera required)

## Usage

### Getting Started
1. **Grant camera permission** when prompted
2. **Download a model** from the Models tab or use bundled models
3. **Select a model** to activate detection
4. **Toggle detection** on/off using the camera controls
5. **Adjust settings** in the Settings tab

### Adding Models
1. Go to the **Models** tab
2. Tap the **"+"** button
3. Enter model name and download URL
4. Or use **"Add Sample Model"** for quick testing
5. Monitor download progress and cancel if needed

### Customizing Detection
1. Open the **Settings** tab
2. Adjust **confidence threshold** (higher = fewer, more accurate detections)
3. Set **processing interval** (higher = better performance, lower accuracy)
4. Toggle **confidence scores** display
5. Set **maximum detections** per frame

## Model Compatibility

### Supported Formats
- `.mlmodel` (CoreML model files)
- `.mlmodelc` (Compiled CoreML models)

### Model Types
- **Object Detection**: YOLOv3, YOLOv5, SSD MobileNet
- **Image Classification**: ResNet, MobileNet, EfficientNet
- **Custom Models**: Any CoreML-compatible model

### Sample Models
The app includes sample model URLs for testing:
- YOLOv3 Object Detection
- MobileNetV2 Classification

## Performance Optimization

### Frame Processing
- **Interval Control**: Process every 5th-10th frame for better performance
- **Confidence Filtering**: Higher thresholds reduce processing overhead
- **Detection Limits**: Limit maximum detections per frame

### Memory Management
- Automatic frame dropping for late processing
- Efficient bounding box calculations
- Background processing queues

## Troubleshooting

### Common Issues

**Camera not working:**
- Ensure camera permissions are granted
- Test on physical device (simulator doesn't have camera)
- Check camera privacy settings

**Model loading fails:**
- Verify model format (.mlmodel or .mlmodelc)
- Check model compatibility with iOS version
- Ensure sufficient storage space

**Poor detection performance:**
- Increase frame processing interval
- Raise confidence threshold
- Reduce maximum detections per frame
- Use smaller/optimized models

**Download failures:**
- Check internet connection
- Verify model URL is accessible
- Ensure sufficient storage space

### Debug Information
- Check Xcode console for detailed error messages
- Monitor memory usage in Instruments
- Use breakpoints in detection processing

## Architecture Details

### Data Flow
1. **Camera** captures frames → `CameraService`
2. **Frames** processed → `DetectionService` 
3. **Results** published → `CameraViewModel`
4. **UI** updates → SwiftUI Views

### State Management
- UserDefaults for persistent settings
- ObservableObject for reactive state
- Combine publishers for data binding

### Error Handling
- Comprehensive error types for each service
- User-friendly error messages
- Graceful degradation for failures

## Customization

### Adding New Model Types
1. Extend `MLModelInfo.ModelType` enum
2. Update model type detection logic
3. Add appropriate UI icons and descriptions

### Custom Detection Logic
1. Modify `DetectionService.performDetection`
2. Implement custom result processing
3. Update UI overlay rendering

### UI Customization
1. Modify colors in `Extensions.swift`
2. Update constants in `Constants.swift`
3. Customize detection box styling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on device
5. Submit a pull request

## License

This project is provided as-is for educational and development purposes.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Xcode console output
3. Test on physical device
4. Verify model compatibility
